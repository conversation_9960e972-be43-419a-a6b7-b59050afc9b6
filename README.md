# QB-HUD - Modern HUD System for QBCore

A modern, transparent HUD system for QBCore Framework with clean design and smooth animations.

## Features

- **Transparent Design**: Semi-transparent boxes with blur effects and clean borders
- **Side-by-Side Layout**: HUD elements arranged horizontally for better screen real estate
- **Responsive**: Adapts to different screen sizes
- **Smooth Animations**: Fade in/out effects and hover animations
- **Low Value Warnings**: Pulsing animations when health, hunger, or thirst are low
- **Vehicle HUD**: Separate HUD for speed and fuel when in vehicles
- **Voice Integration**: Shows current voice range (requires pma-voice)
- **Customizable**: Easy to modify colors, positions, and elements

## HUD Elements

### Player Stats
- **Health**: Red heart icon with health bar
- **Armor**: Blue shield icon (only shows when armor > 0)
- **Hunger**: Orange hamburger icon with hunger bar
- **Thirst**: Green water drop icon with thirst bar
- **Stress**: Purple brain icon (only shows when stress > 0)
- **Voice**: Teal microphone icon showing voice range

### Vehicle Stats (when driving)
- **Speed**: Orange speedometer showing KM/H
- **Fuel**: Yellow gas pump with fuel percentage

## Installation

1. Place the `qb-hud` folder in your `resources/[qb]/` directory
2. Add `ensure qb-hud` to your `server.cfg`
3. Restart your server

## Dependencies

- **qb-core**: Required for player data and framework functions
- **pma-voice**: Optional, for voice range display
- **LegacyFuel** or **ps-fuel**: Optional, for fuel system integration

## Commands

- `/hud` - Toggle HUD visibility on/off

## Configuration

Edit the `Config` table in `client.lua` to customize:

```lua
local Config = {
    UpdateInterval = 1000, -- Update frequency in milliseconds
    VehicleUpdateInterval = 100, -- Vehicle update frequency
    ShowArmor = true, -- Show armor element
    ShowStress = true, -- Show stress element
    ShowVoice = true, -- Show voice element
}
```

## Customization

### Colors
Modify the CSS variables in `html/style.css` to change colors:
- Health: `#e74c3c` (red)
- Armor: `#3498db` (blue)
- Hunger: `#f39c12` (orange)
- Thirst: `#2ecc71` (green)
- Stress: `#9b59b6` (purple)
- Voice: `#1abc9c` (teal)

### Position
Change the position by modifying the CSS in `html/style.css`:
```css
.hud-container {
    bottom: 20px; /* Distance from bottom */
    left: 20px;   /* Distance from left */
}
```

### Adding New Elements
1. Add HTML structure in `html/index.html`
2. Add CSS styling in `html/style.css`
3. Add update logic in `html/script.js`
4. Add data source in `client.lua`

## Exports

```lua
-- Check if HUD is shown
local isShown = exports['qb-hud']:IsHudShown()

-- Toggle HUD programmatically
exports['qb-hud']:ToggleHud(true) -- Show
exports['qb-hud']:ToggleHud(false) -- Hide

-- Update HUD data manually
exports['qb-hud']:UpdateHudData({
    health = 75,
    armor = 50,
    hunger = 80,
    thirst = 60,
    stress = 25
})
```

## Events

### Client Events
- `hud:client:UpdateNeeds` - Update hunger and thirst
- `hud:client:UpdateStress` - Update stress level

### Usage Examples
```lua
-- Update needs
TriggerEvent('hud:client:UpdateNeeds', 75, 60) -- hunger, thirst

-- Update stress
TriggerEvent('hud:client:UpdateStress', 25)
```

## Compatibility

This HUD is designed to work with:
- QBCore Framework
- pma-voice (voice chat)
- LegacyFuel or ps-fuel (fuel systems)
- Most QBCore-compatible servers

## Support

For issues or questions, please check:
1. Console for any error messages
2. Ensure all dependencies are installed
3. Verify resource is started properly

## License

This project is open source and available under the MIT License.
