<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QB-HUD</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="hud-container">
        <!-- Health Box -->
        <div class="hud-box health-box">
            <div class="hud-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Health</div>
                <div class="hud-bar">
                    <div class="hud-fill health-fill" style="width: 100%;"></div>
                </div>
                <div class="hud-value">100</div>
            </div>
        </div>

        <!-- Armor Box -->
        <div class="hud-box armor-box">
            <div class="hud-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Armor</div>
                <div class="hud-bar">
                    <div class="hud-fill armor-fill" style="width: 0%;"></div>
                </div>
                <div class="hud-value">0</div>
            </div>
        </div>

        <!-- Hunger Box -->
        <div class="hud-box hunger-box">
            <div class="hud-icon">
                <i class="fas fa-hamburger"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Hunger</div>
                <div class="hud-bar">
                    <div class="hud-fill hunger-fill" style="width: 75%;"></div>
                </div>
                <div class="hud-value">75</div>
            </div>
        </div>

        <!-- Thirst Box -->
        <div class="hud-box thirst-box">
            <div class="hud-icon">
                <i class="fas fa-tint"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Thirst</div>
                <div class="hud-bar">
                    <div class="hud-fill thirst-fill" style="width: 60%;"></div>
                </div>
                <div class="hud-value">60</div>
            </div>
        </div>

        <!-- Stress Box -->
        <div class="hud-box stress-box">
            <div class="hud-icon">
                <i class="fas fa-brain"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Stress</div>
                <div class="hud-bar">
                    <div class="hud-fill stress-fill" style="width: 25%;"></div>
                </div>
                <div class="hud-value">25</div>
            </div>
        </div>

        <!-- Voice Box -->
        <div class="hud-box voice-box">
            <div class="hud-icon">
                <i class="fas fa-microphone"></i>
            </div>
            <div class="hud-content">
                <div class="hud-label">Voice</div>
                <div class="voice-range">Normal</div>
            </div>
        </div>
    </div>

    <!-- Vehicle HUD (Hidden by default) -->
    <div class="vehicle-hud" style="display: none;">
        <div class="vehicle-box speed-box">
            <div class="vehicle-icon">
                <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="vehicle-content">
                <div class="vehicle-label">Speed</div>
                <div class="vehicle-value">0 KM/H</div>
            </div>
        </div>

        <div class="vehicle-box fuel-box">
            <div class="vehicle-icon">
                <i class="fas fa-gas-pump"></i>
            </div>
            <div class="vehicle-content">
                <div class="vehicle-label">Fuel</div>
                <div class="vehicle-bar">
                    <div class="vehicle-fill fuel-fill" style="width: 100%;"></div>
                </div>
                <div class="vehicle-value">100%</div>
            </div>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="script.js"></script>
</body>
</html>
