// QB-HUD JavaScript
let hudData = {
    health: 100,
    armor: 0,
    hunger: 75,
    thirst: 60,
    stress: 25,
    voice: 1,
    inVehicle: false,
    speed: 0,
    fuel: 100
};

// Voice ranges
const voiceRanges = {
    1: 'Whisper',
    2: 'Normal',
    3: 'Shouting'
};

// Initialize HUD
document.addEventListener('DOMContentLoaded', function() {
    updateHUD();
    
    // Listen for messages from client script
    window.addEventListener('message', function(event) {
        const data = event.data;
        
        switch(data.action) {
            case 'updateHUD':
                updateHUDData(data.data);
                break;
            case 'toggleHUD':
                toggleHUD(data.show);
                break;
            case 'updateVehicle':
                updateVehicleHUD(data.data);
                break;
            case 'toggleVehicleHUD':
                toggleVehicleHUD(data.show);
                break;
        }
    });
});

// Update HUD data
function updateHUDData(data) {
    if (data.health !== undefined) hudData.health = data.health;
    if (data.armor !== undefined) hudData.armor = data.armor;
    if (data.hunger !== undefined) hudData.hunger = data.hunger;
    if (data.thirst !== undefined) hudData.thirst = data.thirst;
    if (data.stress !== undefined) hudData.stress = data.stress;
    if (data.voice !== undefined) hudData.voice = data.voice;
    
    updateHUD();
}

// Update HUD display
function updateHUD() {
    // Health
    updateBar('.health-fill', hudData.health);
    updateValue('.health-box .hud-value', hudData.health);
    toggleLowWarning('.health-box', 'low-health', hudData.health < 25);
    
    // Armor
    updateBar('.armor-fill', hudData.armor);
    updateValue('.armor-box .hud-value', hudData.armor);
    toggleArmorVisibility(hudData.armor > 0);
    
    // Hunger
    updateBar('.hunger-fill', hudData.hunger);
    updateValue('.hunger-box .hud-value', hudData.hunger);
    toggleLowWarning('.hunger-box', 'low-hunger', hudData.hunger < 25);
    
    // Thirst
    updateBar('.thirst-fill', hudData.thirst);
    updateValue('.thirst-box .hud-value', hudData.thirst);
    toggleLowWarning('.thirst-box', 'low-thirst', hudData.thirst < 25);
    
    // Stress
    updateBar('.stress-fill', hudData.stress);
    updateValue('.stress-box .hud-value', hudData.stress);
    toggleStressVisibility(hudData.stress > 0);
    
    // Voice
    updateVoiceRange();
}

// Update progress bar
function updateBar(selector, value) {
    const bar = document.querySelector(selector);
    if (bar) {
        bar.style.width = Math.max(0, Math.min(100, value)) + '%';
    }
}

// Update value display
function updateValue(selector, value) {
    const element = document.querySelector(selector);
    if (element) {
        element.textContent = Math.round(value);
    }
}

// Toggle low value warning
function toggleLowWarning(boxSelector, className, isLow) {
    const box = document.querySelector(boxSelector);
    if (box) {
        if (isLow) {
            box.classList.add(className);
        } else {
            box.classList.remove(className);
        }
    }
}

// Toggle armor visibility
function toggleArmorVisibility(show) {
    const armorBox = document.querySelector('.armor-box');
    if (armorBox) {
        armorBox.style.display = show ? 'flex' : 'none';
    }
}

// Toggle stress visibility
function toggleStressVisibility(show) {
    const stressBox = document.querySelector('.stress-box');
    if (stressBox) {
        stressBox.style.display = show ? 'flex' : 'none';
    }
}

// Update voice range display
function updateVoiceRange() {
    const voiceRange = document.querySelector('.voice-range');
    if (voiceRange) {
        voiceRange.textContent = voiceRanges[hudData.voice] || 'Normal';
        
        // Update color based on range
        switch(hudData.voice) {
            case 1:
                voiceRange.style.color = '#95a5a6';
                break;
            case 2:
                voiceRange.style.color = '#1abc9c';
                break;
            case 3:
                voiceRange.style.color = '#e74c3c';
                break;
        }
    }
}

// Update vehicle HUD
function updateVehicleHUD(data) {
    if (data.speed !== undefined) {
        hudData.speed = data.speed;
        const speedElement = document.querySelector('.speed-box .vehicle-value');
        if (speedElement) {
            speedElement.textContent = Math.round(data.speed) + ' KM/H';
        }
    }
    
    if (data.fuel !== undefined) {
        hudData.fuel = data.fuel;
        updateBar('.fuel-fill', data.fuel);
        const fuelElement = document.querySelector('.fuel-box .vehicle-value');
        if (fuelElement) {
            fuelElement.textContent = Math.round(data.fuel) + '%';
        }
    }
}

// Toggle vehicle HUD visibility
function toggleVehicleHUD(show) {
    const vehicleHUD = document.querySelector('.vehicle-hud');
    if (vehicleHUD) {
        if (show) {
            vehicleHUD.style.display = 'flex';
            vehicleHUD.classList.add('fade-in');
            vehicleHUD.classList.remove('fade-out');
        } else {
            vehicleHUD.classList.add('fade-out');
            vehicleHUD.classList.remove('fade-in');
            setTimeout(() => {
                vehicleHUD.style.display = 'none';
            }, 500);
        }
    }
    hudData.inVehicle = show;
}

// Toggle main HUD visibility
function toggleHUD(show) {
    const hudContainer = document.querySelector('.hud-container');
    if (hudContainer) {
        if (show) {
            hudContainer.style.display = 'flex';
            hudContainer.classList.add('fade-in');
            hudContainer.classList.remove('fade-out');
        } else {
            hudContainer.classList.add('fade-out');
            hudContainer.classList.remove('fade-in');
            setTimeout(() => {
                hudContainer.style.display = 'none';
            }, 500);
        }
    }
}

// Send data to client script
function sendToClient(data) {
    fetch(`https://${GetParentResourceName()}/hudCallback`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify(data)
    });
}

// Debug function for testing
function debugHUD() {
    console.log('Current HUD Data:', hudData);
    
    // Test low values
    setTimeout(() => {
        updateHUDData({
            health: 15,
            hunger: 10,
            thirst: 5
        });
    }, 2000);
    
    // Test vehicle HUD
    setTimeout(() => {
        toggleVehicleHUD(true);
        updateVehicleHUD({
            speed: 120,
            fuel: 45
        });
    }, 4000);
}
