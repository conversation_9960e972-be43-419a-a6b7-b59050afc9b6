* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    background: transparent;
    user-select: none;
}

/* Main HUD Container */
.hud-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    display: flex;
    gap: 15px;
    z-index: 1000;
}

/* Individual HUD Boxes */
.hud-box {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px;
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 10px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.hud-box:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* HUD Icons */
.hud-icon {
    font-size: 20px;
    width: 30px;
    text-align: center;
    color: #fff;
}

.health-box .hud-icon {
    color: #e74c3c;
}

.armor-box .hud-icon {
    color: #3498db;
}

.hunger-box .hud-icon {
    color: #f39c12;
}

.thirst-box .hud-icon {
    color: #2ecc71;
}

.stress-box .hud-icon {
    color: #9b59b6;
}

.voice-box .hud-icon {
    color: #1abc9c;
}

/* HUD Content */
.hud-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.hud-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.hud-value {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
}

/* Progress Bars */
.hud-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.hud-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.5s ease;
    position: relative;
}

.health-fill {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.armor-fill {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.hunger-fill {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.thirst-fill {
    background: linear-gradient(90deg, #2ecc71, #27ae60);
}

.stress-fill {
    background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

/* Voice Range */
.voice-range {
    font-size: 12px;
    color: #1abc9c;
    font-weight: bold;
    text-transform: uppercase;
}

/* Vehicle HUD */
.vehicle-hud {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 15px;
    z-index: 1000;
}

.vehicle-box {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px;
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 10px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.vehicle-box:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.vehicle-icon {
    font-size: 20px;
    width: 30px;
    text-align: center;
    color: #fff;
}

.speed-box .vehicle-icon {
    color: #e67e22;
}

.fuel-box .vehicle-icon {
    color: #f1c40f;
}

.vehicle-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.vehicle-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.vehicle-value {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
}

.vehicle-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.vehicle-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.5s ease;
    position: relative;
}

.fuel-fill {
    background: linear-gradient(90deg, #f1c40f, #f39c12);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hud-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .vehicle-hud {
        flex-direction: column;
        gap: 10px;
    }
    
    .hud-box, .vehicle-box {
        min-width: 100px;
        padding: 10px;
    }
}

/* Animation for low values */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.hud-box.low-health .health-fill,
.hud-box.low-hunger .hunger-fill,
.hud-box.low-thirst .thirst-fill {
    animation: pulse 1.5s infinite;
}

/* Hide/Show animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}
