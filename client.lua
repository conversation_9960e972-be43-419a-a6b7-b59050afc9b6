local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isLoggedIn = false
local hudShown = true

-- HUD Configuration
local Config = {
    UpdateInterval = 1000, -- Update every 1 second
    VehicleUpdateInterval = 100, -- Update vehicle info every 100ms
    ShowArmor = true,
    ShowStress = true,
    ShowVoice = true,
}

-- Initialize
CreateThread(function()
    while true do
        Wait(1000)
        if LocalPlayer.state.isLoggedIn then
            isLoggedIn = true
            PlayerData = QBCore.Functions.GetPlayerData()
            break
        end
    end
end)

-- Main HUD Update Loop
CreateThread(function()
    while true do
        Wait(Config.UpdateInterval)
        
        if isLoggedIn and hudShown then
            local player = PlayerPedId()
            local playerId = PlayerId()
            
            -- Get player stats
            local health = GetEntityHealth(player) - 100
            local armor = GetPedArmour(player)
            
            -- Get metadata (hunger, thirst, stress)
            local hunger = 100
            local thirst = 100
            local stress = 0
            
            if PlayerData.metadata then
                hunger = PlayerData.metadata.hunger or 100
                thirst = PlayerData.metadata.thirst or 100
                stress = PlayerData.metadata.stress or 0
            end
            
            -- Get voice range
            local voiceRange = 2 -- Default to normal
            if exports['pma-voice'] then
                local voiceData = exports['pma-voice']:getPlayerData(playerId)
                if voiceData then
                    voiceRange = voiceData.range or 2
                end
            end
            
            -- Send data to NUI
            SendNUIMessage({
                action = 'updateHUD',
                data = {
                    health = math.max(0, health),
                    armor = armor,
                    hunger = hunger,
                    thirst = thirst,
                    stress = stress,
                    voice = voiceRange
                }
            })
        end
    end
end)

-- Vehicle HUD Update Loop
CreateThread(function()
    while true do
        Wait(Config.VehicleUpdateInterval)
        
        if isLoggedIn and hudShown then
            local player = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(player, false)
            
            if vehicle ~= 0 and GetPedInVehicleSeat(vehicle, -1) == player then
                -- Player is driving a vehicle
                local speed = GetEntitySpeed(vehicle) * 3.6 -- Convert to KM/H
                local fuel = 100 -- Default fuel
                
                -- Get fuel level (if using a fuel system)
                if exports['LegacyFuel'] then
                    fuel = exports['LegacyFuel']:GetFuel(vehicle)
                elseif exports['ps-fuel'] then
                    fuel = exports['ps-fuel']:GetFuel(vehicle)
                elseif GetVehicleFuelLevel then
                    fuel = GetVehicleFuelLevel(vehicle)
                end
                
                -- Show vehicle HUD
                SendNUIMessage({
                    action = 'toggleVehicleHUD',
                    show = true
                })
                
                -- Update vehicle data
                SendNUIMessage({
                    action = 'updateVehicle',
                    data = {
                        speed = speed,
                        fuel = fuel
                    }
                })
            else
                -- Player is not in a vehicle
                SendNUIMessage({
                    action = 'toggleVehicleHUD',
                    show = false
                })
            end
        end
    end
end)

-- Event Handlers
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    isLoggedIn = true
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    isLoggedIn = false
    PlayerData = {}
end)

RegisterNetEvent('QBCore:Player:SetPlayerData', function(val)
    PlayerData = val
end)

RegisterNetEvent('hud:client:UpdateNeeds', function(newHunger, newThirst)
    if PlayerData.metadata then
        PlayerData.metadata.hunger = newHunger
        PlayerData.metadata.thirst = newThirst
    end
end)

RegisterNetEvent('hud:client:UpdateStress', function(newStress)
    if PlayerData.metadata then
        PlayerData.metadata.stress = newStress
    end
end)

-- Commands
RegisterCommand('hud', function()
    hudShown = not hudShown
    SendNUIMessage({
        action = 'toggleHUD',
        show = hudShown
    })
    
    if hudShown then
        QBCore.Functions.Notify('HUD Enabled', 'success')
    else
        QBCore.Functions.Notify('HUD Disabled', 'error')
    end
end)

-- NUI Callbacks
RegisterNUICallback('hudCallback', function(data, cb)
    -- Handle any callbacks from the NUI
    cb('ok')
end)

-- Exports
exports('IsHudShown', function()
    return hudShown
end)

exports('ToggleHud', function(state)
    hudShown = state
    SendNUIMessage({
        action = 'toggleHUD',
        show = hudShown
    })
end)

exports('UpdateHudData', function(data)
    SendNUIMessage({
        action = 'updateHUD',
        data = data
    })
end)
